/* 移动端样式修复 - 解决文字过大问题 */

/* 全局字体大小调整 */
@media screen and (max-width: 768px) {
  /* 常规文本大小 */
  body {
    font-size: 1.4rem;
  }

  /* 标题大小调整 */
  h1,
  .h1-size {
    font-size: 2.2rem !important;
  }

  h2,
  .h2-size {
    font-size: 2rem !important;
  }

  h3,
  .h3-size {
    font-size: 1.8rem !important;
  }

  /* 按钮文本大小 */
  button,
  .btn {
    font-size: 1.4rem !important;
  }

  /* 导航菜单项 */
  .header_view_nav_item {
    font-size: 1.6rem !important;
  }

  /* 产品介绍区域 */
  .home_view_introduce {
    &_title {
      font-size: 2.2rem !important;
    }

    &_desc {
      font-size: 1.6rem !important;
    }

    &_list_item {
      &_name {
        font-size: 2.2rem !important;
      }

      &_desc {
        font-size: 1.4rem !important;
      }
    }
  }

  /* Ledger介绍区域 */
  .home_view_ledger {
    &_main {
      p {
        font-size: 1.6rem !important;
      }

      .puper {
        font-size: 1.8rem !important;
      }
    }
  }

  /* 商品列表 */
  .goods_item {
    &_title {
      font-size: 1.6rem !important;
    }

    &_price {
      font-size: 1.8rem !important;
    }

    &_desc {
      font-size: 1.4rem !important;
    }
  }

  /* 按钮样式调整 */
  .home_view_product_title_btn {
    padding: 0 1.5rem !important;
    height: 4rem !important;
  }

  /* 商品列表页面样式调整 */
  .goods-list-main {
    .banner-box_text {
      &_title {
        font-size: 2.6rem !important;
        line-height: 1.3 !important;
      }

      &_desc {
        font-size: 1.6rem !important;
        line-height: 1.5 !important;
      }
    }

    .goods_view {
      &_title {
        font-size: 2.4rem !important;
        line-height: 1.3 !important;
      }
    }
  }

  /* 商品列表组件样式调整 */
  .product_list {
    /* 非单列模式下的商品列表样式（Goods和Wish页面） */
    &:not(.single-column) {
      display: grid !important;
      grid-template-columns: 1fr 1fr !important; /* 两列布局 */
      gap: 1rem !important;
    }

    /* 单列模式下的商品列表样式（首页） */
    &.single-column {
      .product_list_item {
        width: 100% !important;
      }
    }

    &_item {
      height: auto !important;
      padding: 1.5rem !important;

      /* 只在非单列模式下应用宽度100% */
      &:not(.single-column-item) {
        width: 100% !important;
      }

      &_img {
        margin-bottom: 1rem !important;
        height: auto !important;
        // max-height: 15rem !important;
        width: 100% !important;
        object-fit: cover !important;
        align-self: center;
      }

      &_name {
        font-size: 1.6rem !important;
        height: auto !important;
        line-height: 1.3 !important;
        margin-bottom: 1rem !important;
        max-height: 3.6rem !important;
        overflow: hidden !important;
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        line-clamp: 2 !important; /* 标准属性，配合-webkit-前缀实现兼容性 */
        white-space: normal !important;
      }

      &_info {
        gap: 0.5rem !important;
        flex-wrap: wrap !important;

        &_price {
          font-size: 1.4rem !important;
          height: 2.4rem !important;
          padding: 0 0.6rem !important;
        }

        &_sale {
          font-size: 1.4rem !important;
        }
      }
    }

    /* 单列图片样式特殊处理 */
    &.single-column .product_list_item_img {
      width: 100% !important;
      // max-width: 25rem !important;
      // max-height: 25rem !important;
      object-fit: contain !important;
    }
  }

  /* 收藏夹页面样式调整 */
  .wish_box {
    height: auto !important;
    padding: 3rem 1.5rem !important;

    &_title {
      font-size: 2.6rem !important;
      height: auto !important;
      line-height: 1.3 !important;
      margin-bottom: 1rem !important;
    }

    &_desc {
      font-size: 1.6rem !important;
      height: auto !important;
      line-height: 1.5 !important;
    }
  }

  .wish_view {
    padding: 2rem 1rem !important;
  }

  .empty_state {
    padding: 2rem !important;
    font-size: 1.4rem !important;
  }

  /* 商品详情页面移动端样式 */
  .goods_box {
    padding-top: 1.5rem !important;

    &_title {
      margin-bottom: 1.5rem !important;
      padding: 0 1.5rem !important;
      font-size: 1.2rem !important;
    }

    &_info {
      padding: 0 1rem !important;
      flex-direction: column !important;

      /* 图片展示区域样式 */
      &_img {
        width: 100% !important;
        flex-direction: column !important;
        margin-bottom: 2rem !important;

        /* 大图区域放在上方 */
        &_big {
          width: 100% !important;
          margin-bottom: 1rem !important;
          order: 1; /* 改变DOM顺序，让大图在前 */

          &_img {
            width: 100% !important;
            height: auto !important;
            max-height: 30rem !important;
            object-fit: contain !important;
          }
        }

        /* 小图预览区放在下方，水平滚动 */
        &_list {
          width: 100% !important;
          height: auto !important;
          display: flex !important;
          flex-direction: row !important;
          overflow-x: scroll !important; /* 简单直接地设置为可滚动 */
          overflow-y: hidden !important;
          margin-right: 0 !important;
          order: 2; /* 改变DOM顺序，让小图列表在后 */
          padding: 0.8rem 0 !important;

          /* 隐藏滚动条但保持功能 */
          &::-webkit-scrollbar {
            display: none !important;
          }
          scrollbar-width: none !important; /* Firefox */

          &_single {
            width: 8rem !important;
            height: 8rem !important;
            min-width: 8rem !important; /* 固定宽度，防止挤压 */
            margin: 0 0.5rem !important;
            display: inline-flex !important;
            flex-shrink: 0 !important; /* 防止被压缩 */
            align-items: center !important;
            justify-content: center !important;
            border-radius: 0.5rem !important;

            &_img {
              width: 7rem !important; /* 稍微小于容器，确保有边距 */
              height: 7rem !important;
              object-fit: contain !important; /* 保持图片比例 */
            }
          }
        }
      }

      /* 商品信息区域样式 */
      &_base {
        height: auto !important;
        padding-left: 0 !important;

        &_name {
          font-size: 2rem !important;
          margin-bottom: 0.5rem !important;
        }

        &_price {
          font-size: 1.8rem !important;
          height: 3rem !important;
          line-height: 3rem !important;
          margin-bottom: 1.5rem !important;
        }

        &_property {
          margin-bottom: 0.5rem !important;
          font-size: 1.4rem !important;
        }

        &_action {
          display: flex !important;
          flex-wrap: wrap !important; /* 允许在需要时换行 */
          gap: 1rem !important;
          margin-top: 1.5rem !important;
          justify-content: space-between !important; /* 按钮两端对齐 */

          &_item {
            width: 48% !important; /* 设置为略小于50%，留出间距 */
            min-height: 4.5rem !important; /* 最小高度而不是固定高度 */
            height: auto !important; /* 自适应高度 */
            padding: 1rem !important; /* 增加内边距以适应多行文字 */
            line-height: 1.3 !important; /* 行高调整，适应多行 */
            font-size: 1.4rem !important; /* 略微减小字体 */
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            word-break: break-word !important; /* 允许在任何字符处换行 */
            white-space: normal !important; /* 允许文本正常换行 */
          }

          &_item.addcart {
            margin-right: 0 !important;
          }
        }

        &_collect {
          margin-top: 1rem !important;
          font-size: 1.6rem !important;
        }
      }
    }

    &_description {
      padding: 1.5rem !important;
      margin-top: 1.5rem !important;
    }
  }

  /* 下载页面样式调整 */
  .download_box {
    .banner-box_text {
      &_title {
        font-size: 2.6rem !important;
        line-height: 1.3 !important;
      }

      &_desc {
        font-size: 1.6rem !important;
        line-height: 1.5 !important;
      }
    }

    &_info {
      &_name {
        font-size: 2.4rem !important;
      }

      &_desc {
        font-size: 1.6rem !important;
        line-height: 1.5 !important;
      }

      &_tip {
        font-size: 1.4rem !important;
      }

      &_list_item_main_label {
        &_title {
          font-size: 1.2rem !important;
        }

        &_desc {
          font-size: 1.8rem !important;
        }
      }
    }
  }
}
