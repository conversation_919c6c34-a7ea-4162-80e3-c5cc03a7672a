<template>
  <header
    class="header"
    :class="{
      'header-mobile': deviceStore.isMobile,
      'header-tablet': deviceStore.isTablet,
    }"
  >
    <div class="header_view">
      <img
        v-if="!deviceStore.isMobile"
        src="@assets/logo.png"
        class="header_view_logo"
      />
      <img v-else src="@assets/<EMAIL>" class="header_view_logo" />

      <!-- 导航菜单 -->
      <div
        class="header_view_nav"
        :class="{ 'mobile-nav-open': mobileMenuOpen && deviceStore.isMobile }"
      >
        <!-- 移动端币种和语言切换 (右上角) -->
        <div v-if="deviceStore.isMobile" class="mobile-settings-header">
          <div class="mobile-settings-row">
            <!-- 币种切换 -->
            <el-dropdown
              trigger="click"
              @command="onChange"
              class="mobile-dropdown"
            >
              <span class="mobile-dropdown-trigger">
                {{ unit }}
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in unitList"
                    :key="item.id"
                    :command="item.code"
                  >
                    <span
                      class="currency-symbol"
                      v-if="item.code == 'CNY'"
                      style="margin-right: 0.9rem"
                    >
                      {{ item.symbol_left }}
                    </span>
                    <span class="currency-symbol" v-else
                      >{{ item.symbol_left }}
                    </span>
                    {{ item.code }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 语言切换 -->
            <el-dropdown
              trigger="click"
              @command="onChangeLang"
              class="mobile-dropdown"
            >
              <span class="mobile-dropdown-trigger">
                {{ getCurrentLanguageName() }}
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in langList"
                    :key="item.id"
                    :command="item.name"
                  >
                    {{ item.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/') }"
          @click="onGoUrl('/')"
        >
          {{ t('app.home') }}
        </div>
        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/download') }"
          @click="onGoUrl('/download')"
        >
          {{ t('app.app') }}
        </div>
        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/goods') }"
          @click="onGoUrl('/goods')"
        >
          {{ t('app.product') }}
        </div>
        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/wish') }"
          @click="onGoUrl('/wish')"
        >
          {{ t('app.like') }}
        </div>
      </div>
      <!-- 搜索框 -->
      <div class="search-box-container" v-if="showSearchBox">
        <div class="search-box">
          <div class="search-input-wrapper">
            <el-input
              v-model="searchKeyword"
              :placeholder="t('shu_ru_jin_hang_cha_xun')"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <img src="@assets/search.png" class="search-icon" />
              </template>
            </el-input>
          </div>
          <img
            src="@assets/<EMAIL>"
            class="close-search-btn"
            @click="toggleSearchBox"
          />
        </div>
      </div>
      <div
        class="header_view_action"
        :class="{ 'mobile-actions': deviceStore.isMobile }"
      >
        <!-- 搜索按钮 - 移动端显示 -->
        <!-- <div
          class="header_view_action_item"
          v-if="deviceStore.isMobile"
          @click="toggleSearchBox"
        >
          <img src="@assets/search.png" class="header_view_action_item_icon" />
        </div> -->

        <!-- 用户按钮 -->
        <div class="header_view_action_item">
          <img
            src="@assets/user.png"
            class="header_view_action_item_icon"
            @click="onGoUrl('/user')"
          />
        </div>

        <!-- 购物车按钮 -->
        <div
          class="header_view_action_item cart-icon-container"
          @click="drawer = true"
        >
          <img src="@assets/cart.png" class="header_view_action_item_icon" />
          <span v-if="cartCount > 0" class="cart-count">{{ cartCount }}</span>
        </div>

        <!-- 移动端菜单按钮 -->
        <div
          class="header_view_action_item"
          v-if="deviceStore.isMobile"
          @click="toggleMobileMenu"
        >
          <img
            v-if="!mobileMenuOpen"
            src="@assets/h5/<EMAIL>"
            class="header_view_action_item_icon"
          />
          <img
            v-else
            src="@assets/close.png"
            class="header_view_action_item_icon"
          />
        </div>

        <!-- 桌面端才显示的操作项 -->
        <template v-if="!deviceStore.isMobile">
          <div class="header_view_action_line"></div>
          <el-dropdown
            class="header_view_action_item"
            trigger="click"
            @command="onChange"
          >
            <span class="el-dropdown-link">
              {{ unit }}
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in unitList"
                  :key="item.id"
                  :command="item.code"
                >
                  <span
                    class="currency-symbol"
                    v-if="item.code == 'CNY'"
                    style="margin-right: 0.9rem"
                  >
                    {{ item.symbol_left }}
                  </span>
                  <span class="currency-symbol" v-else
                    >{{ item.symbol_left }}
                  </span>
                  {{ item.code }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown
            class="header_view_action_item"
            trigger="click"
            @command="onChangeLang"
          >
            <span class="el-dropdown-link">
              {{ getCurrentLanguageName() }}
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in langList"
                  :key="item.id"
                  :command="item.name"
                >
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </div>
    </div>
  </header>
  <router-view></router-view>
  <footer class="footer" :class="{ 'footer-mobile': deviceStore.isMobile }">
    <div class="footer_main">
      <h4
        class="footer_main_title"
        :style="deviceStore.isMobile ? 'font-size:2.2rem' : ''"
      >
        {{ t('lian_xi_wo_men') }}
      </h4>
      <div
        class="footer_main_tip"
        :style="deviceStore.isMobile ? 'font-size:1.4rem' : ''"
      >
        {{ t('ke_yi_zai_wo_men_de_bo_ke_cha_kan_gong_gao') }}
      </div>
      <div
        class="footer_main_link"
        v-if="contactInfo && contactInfo.whatsapp"
        @click="openLink(contactInfo.whatsapp)"
      >
        WhatsApp：<span>{{ contactInfo.whatsapp }}</span>
      </div>
      <div
        class="footer_main_concat"
        :style="deviceStore.isMobile ? 'flex-wrap: wrap' : ''"
      >
        <div
          class="footer_main_concat_f"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        >
          <img src="@assets/f.png" class="footer_main_concat_f_icon" />
        </div>
        <img
          src="@assets/x.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
        <img
          src="@assets/i.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
        <img
          src="@assets/youtube.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
        <img
          src="@assets/douyin.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
      </div>
    </div>
    <div class="footer_info">
      <div
        class="footer_info_title"
        :style="deviceStore.isMobile ? 'font-size:2.2rem' : ''"
      >
        {{ t('ding_yue_wo_men_de_shi_shi_tong_xun') }}
      </div>
      <div
        class="footer_info_desc"
        :style="deviceStore.isMobile ? 'font-size:1.4rem' : ''"
      >
        {{
          t(
            'zhi_chi_xin_bi_zhong_bo_ke_geng_xin_he_du_jia_you_hui_zhi_jie_fa_song_dao_nin_de_shou_jian_xiang'
          )
        }}
      </div>
      <div class="footer_info_action">
        <input
          class="footer_info_action_input"
          :placeholder="t('qing_shu_ru_nin_de_dian_zi_you_xiang')"
        />
        <div class="footer_info_action_btn" @click="onBind">
          {{ t('ding_yue_xin_wen') }}
          <img src="@assets/arrow.png" class="footer_info_action_btn_icon" />
        </div>
      </div>
      <div class="footer_info_tip">
        {{
          t(
            'nin_de_dian_zi_you_jian_di_zhi_jin_yong_yu_xiang_nin_fa_song_wo_men_de_xin_wen_tong_xun_zui_xin_zi_xun_he_you_hui_xin_xi_nin_ke_yi_sui_shi_tong_guo_xin_wen_tong_xun_zhong_de_lian_jie_qu_xiao_ding_yue_le_jie_geng_duo_guan_yu_wo_men_ru_he_guan_li_nin_de_shu_ju_he_nin_de_quan_li'
          )
        }}
      </div>
    </div>
  </footer>
  <el-drawer
    v-model="drawer"
    :show-close="false"
    title=""
    :direction="direction"
    :width="deviceStore.isMobile ? '100%' : '465px'"
  >
    <template #header>
      <div class="drawer_header">
        <div class="drawer_header_main">
          {{ t('cart.cart') }}
          <span></span>
          <img
            class="drawer_header_icon"
            src="@assets/close.png"
            @click="drawer = false"
          />
        </div>
      </div>
    </template>
    <div class="isEmpty" v-if="isEmpty">
      <img src="@assets/cart_none.png" class="isEmpty_img" />
      <div class="isEmpty_text">{{ t('gou_wu_che_li_shi_mo_ye_mei_you') }}</div>
      <div class="isEmpty_subtext">
        {{ t('gan_kuai_qu_shang_dian_li_mian_guang_guang_ba') }}
      </div>
      <div
        class="isEmpty_action"
        @click="
          drawer = false;
          onGoUrl('/goods');
        "
      >
        {{ t('qu_guang_guang') }}
      </div>
    </div>
    <div class="cart_view" v-else>
      <div v-if="loading" class="loading-state" v-loading="loading"></div>
      <div v-else>
        <div
          class="cart_view_item"
          v-for="item in cartList"
          :key="item.cart_id"
        >
          <el-checkbox
            :model-value="item.selected == 1"
            @change="(checked) => handleSelectItem(item.cart_id, checked)"
            class="cart_view_item_checkbox"
          ></el-checkbox>
          <img :src="item.image_url" class="cart_view_item_img" />
          <div class="cart_view_item_main">
            <p class="cart_view_item_name">{{ item.name_format }}</p>
            <span class="cart_view_item_price">{{ item.price_format }}</span>
            <el-input-number
              :model-value="item.quantity"
              :min="1"
              :max="item.stock"
              @change="(value) => handleUpdateQuantity(item.cart_id, value)"
              :step="1"
              size="small"
            />
            <img
              src="@assets/trash.png"
              class="cart_view_item_del"
              @click="handleRemoveItem(item.cart_id)"
            />
          </div>
        </div>
      </div>
    </div>
    <template #footer v-if="!isEmpty">
      <div class="cart_view_footer">
        <el-checkbox
          :model-value="selectAll"
          @change="handleSelectAll"
          class="cart_view_footer_checkbox"
          >{{ t('cart.chooseAll') }} ({{ selectedCount }})</el-checkbox
        >
        <div class="cart_view_footer_total">{{ totalAmount }}</div>
        <div
          class="cart_view_footer_submit"
          @click="handleCheckout"
          :class="{ disabled: selectedCount === 0 }"
          :style="deviceStore.isMobile ? 'margin-bottom: 0.6rem' : ''"
        >
          {{ t('cart.toPayment') }}
        </div>
        <div class="cart_view_footer_cart" @click="handleViewCart">
          {{ t('cart.action') }}
        </div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup>
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowDown, ShoppingCart } from '@element-plus/icons-vue';
import { useDeviceStore } from '@/stores/device';
import {
  GetCartList,
  SelectCartItems,
  UnselectCartItems,
  UpdateCart,
  RemoveCartItem,
} from '@api/cart';
import {
  setLanguage,
  getLanguage,
  setCurrency,
  getCurrency,
} from '@utils/storage';
import {
  cartUpdated,
  currencyUpdated,
  triggerCurrencyUpdate,
} from '@utils/eventBus'; // 导入更新通知
const { t, locale } = useI18n();
import {
  GetHomeInfo,
  GetSupportedLanguages,
  GetSupportedCurrencies,
  SetCurrency,
  SetLanguage,
} from '@api';

const router = useRouter();
const deviceStore = useDeviceStore();
const unit = ref('USD');
const unitList = ref([]);

// 移动端相关状态
const mobileMenuOpen = ref(false);

const langList = ref([
  {
    id: 0,
    name: 'Español',
    lang: 'es', // 确保与 i18n 配置中的键名一致
  },
  {
    id: 1,
    name: 'Français',
    lang: 'fr',
  },
  {
    id: 2,
    name: 'Indonesia',
    lang: 'id',
  },
  {
    id: 3,
    name: 'Italiano',
    lang: 'it',
  },
  {
    id: 4,
    name: '日本語',
    lang: 'ja',
  },
  {
    id: 5,
    name: '한국어',
    lang: 'ko',
  },
  {
    id: 6,
    name: 'Русский',
    lang: 'Py',
  },
  {
    id: 7,
    name: '繁體中文',
    lang: 'hk',
  },
  {
    id: 8,
    name: 'Deutsch',
    lang: 'de',
  },
  {
    id: 9,
    name: '简体中文',
    lang: 'zh',
  },
  {
    id: 10,
    name: 'English',
    lang: 'en',
  },
]);

const onBind = () => {
  ElMessage.success(t('ding_yue_cheng_gong'));
};

const openLink = (account) => {
  // 打开WhatsApp 链接
  const whatsappUrl = `https://wa.me/${account}`;
  window.open(whatsappUrl, '_blank');
};

const onChangeLang = async (lang) => {
  try {
    // 获取语言对应的locale代码
    const langItem = langList.value.find((item) => item.name === lang);
    if (langItem) {
      // 调用接口设置语言
      locale.value = langItem.lang; // 使用语言代码而不是名称
      let tempLang = langItem.lang;
      if (langItem.lang == 'zh') {
        tempLang = 'zh_cn';
      } else if (langItem.lang == 'hk') {
        tempLang = 'zh_hk';
      }
      // await SetLanguage(tempLang);

      // 更新本地UI和存储
      locale.value = langItem.lang; // 使用语言代码而不是名称
      setLanguage(langItem.lang);
    }
  } catch (error) {
    console.error('设置语言失败:', error);
  }
};

const searchKeyword = ref('');
const showSearchBox = ref(false);
const drawer = ref(false);
const direction = ref('rtl');

// 购物车相关数据
const cartList = ref([]);
const loading = ref(false);
const selectAll = ref(false);

// 计算属性
const isEmpty = computed(() => cartList.value.length === 0);

const selectedItems = computed(() => {
  return cartList.value.filter((item) => item.selected);
});

const selectedCount = computed(() => {
  return selectedItems.value.length;
});

// 计算购物车中的商品总数
const cartCount = computed(() => {
  return cartList.value.length;
});

const totalAmount = computed(() => {
  const total = selectedItems.value.reduce((sum, item) => {
    return sum + item.price * item.quantity;
  }, 0);
  return `$${total.toFixed(2)}`;
});

const contactInfo = ref({});

// 监听选中状态变化，更新全选状态
watch(
  cartList,
  () => {
    selectAll.value =
      cartList.value.length > 0 &&
      cartList.value.every((item) => item.selected);
  },
  { deep: true }
);

// 监听抽屉打开，加载购物车数据
watch(drawer, (isOpen) => {
  if (isOpen) {
    loadCartList();
  }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
  deviceStore.cleanup(); // 清理设备检测相关的事件监听
});

// 监听购物车更新事件
watch(cartUpdated, () => {
  loadCartList();
});

// 移动端菜单切换
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

// 点击导航项时关闭移动端菜单
const onGoUrl = (url) => {
  router.push(url);
  mobileMenuOpen.value = false; // 导航后关闭菜单
};

// 初始化组件时检查语言设置和加载购物车数据
onMounted(() => {
  // 初始化设备检测
  deviceStore.initDevice();

  // 从本地存储获取保存的语言和币种
  const savedLanguage = getLanguage();
  const savedCurrency = getCurrency();

  if (savedLanguage) {
    // 确保是有效的语言代码
    const isValidLang = langList.value.some(
      (item) => item.lang === savedLanguage
    );
    locale.value = isValidLang ? savedLanguage : 'en';
  }

  if (savedCurrency) {
    unit.value = savedCurrency;
  }

  // 加载支持的语言列表
  loadSupportedLanguages();

  // 加载支持的币种列表
  loadSupportedCurrencies();

  // 加载购物车数据
  loadCartList();
  loadInfo();
});

// 加载支持的语言列表
const loadSupportedLanguages = async () => {
  try {
    const response = await GetSupportedLanguages();
    console.log('获取语言列表', response);
    if (response && response.list) {
      // langList.value = response.list.map((item) => ({
      //   ...item,
      //   name: item.name || item.lang, // 确保有name字段
      // }));
    }
    if (response && response.current) {
      let lang = response.current.code;
      if (lang == 'zh_cn') {
        lang = 'zh';
      } else if (lang == 'zh_hk') {
        lang = 'hk';
      }
      locale.value = lang;
      setLanguage(lang);
    }
  } catch (error) {
    console.error('获取语言列表失败:', error);
  }
};

// 加载支持的币种列表
const loadSupportedCurrencies = async () => {
  try {
    const response = await GetSupportedCurrencies();
    console.log('获取币种列表', response);
    if (response && response.list) {
      unitList.value = response.list.map((item) => ({
        ...item,
      }));
    }
    if (response && response.current) {
      unit.value = response.current;
      setCurrency(response.current);
    }
  } catch (error) {
    console.error('获取币种列表失败:', error);
  }
};

// 监听路由变化，重新获取购物车数据
watch(
  () => router.currentRoute.value.path,
  () => {
    loadCartList();
  }
);

const loadInfo = () => {
  GetHomeInfo()
    .then((res) => {
      if (res) {
        contactInfo.value = res.content;
      }
    })
    .catch((error) => {
      console.error('获取数据失败:', error);
    });
};

// 获取购物车列表
const loadCartList = async () => {
  try {
    loading.value = true;
    const response = await GetCartList();

    if (response && response.carts) {
      cartList.value = response.carts.map((item) => ({
        cart_id: item.cart_id,
        name_format: item.name_format,
        image_url: item.image_url,
        price: item.price,
        price_format: item.price_format,
        quantity: item.quantity,
        stock: item.stock,
        selected: item.selected || false,
        sku_id: item.sku_id,
      }));
    }
  } catch (error) {
    console.error('获取购物车列表失败:', error);
    // ElMessage.error('获取购物车列表失败');
  } finally {
    loading.value = false;
  }
};

// 全选/取消全选
const handleSelectAll = async (checked) => {
  try {
    const cartIds = cartList.value.map((item) => item.cart_id);

    // 先在本地更新选中状态，提供即时反馈
    cartList.value.forEach((item) => (item.selected = checked));

    // 异步调用API更新服务器数据
    if (checked) {
      await SelectCartItems(cartIds);
    } else {
      await UnselectCartItems(cartIds);
    }
  } catch (error) {
    console.error('批量选择失败:', error);
    // ElMessage.error('操作失败');

    // 操作失败时回滚状态
    cartList.value.forEach((item) => (item.selected = !checked));
    selectAll.value = !checked;
  }
};

// 选中/取消选中单个商品
const handleSelectItem = async (cartId, checked) => {
  try {
    // 先在本地更新选中状态，提供即时反馈
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (item) {
      item.selected = checked;
    }

    // 异步调用API更新服务器数据
    if (checked) {
      await SelectCartItems([cartId]);
    } else {
      await UnselectCartItems([cartId]);
    }
  } catch (error) {
    console.error('选择商品失败:', error);
    // ElMessage.error('操作失败');

    // 操作失败时回滚状态
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (item) {
      item.selected = !checked;
    }
  }
};

// 更新商品数量
const handleUpdateQuantity = async (cartId, quantity) => {
  try {
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (!item) return;

    // 先在本地更新数量，提供即时反馈
    const originalQuantity = item.quantity;
    item.quantity = quantity;

    // 异步调用API更新服务器数据
    await UpdateCart(cartId, {
      quantity: quantity,
      sku_id: item.sku_id,
    });

    // 成功时轻提示
    // ElMessage.success('数量更新成功');
  } catch (error) {
    console.error('更新数量失败:', error);
    // ElMessage.error('更新数量失败');

    // 只有在发生错误时才重新加载购物车列表
    loadCartList();
  }
};

// 删除商品
const handleRemoveItem = async (cartId) => {
  try {
    // 先在本地删除商品，提供即时反馈
    const originalList = [...cartList.value];
    cartList.value = cartList.value.filter((item) => item.cart_id !== cartId);

    // 异步调用API更新服务器数据
    await RemoveCartItem(cartId);

    ElMessage.success(t('shang_pin_yi_shan_chu'));
  } catch (error) {
    console.error('删除商品失败:', error);
    // ElMessage.error('删除商品失败');

    // 删除失败时重新加载购物车
    loadCartList();
  }
};

// 查看购物车 - 跳转到第一步
const handleViewCart = () => {
  drawer.value = false;
  router.push({ path: '/confirm', query: { step: 1 } });
};

// 去结算 - 跳转到第二步
const handleCheckout = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning(t('qing_xuan_ze_shang_pin'));
    return;
  }
  drawer.value = false;
  router.push({ path: '/confirm', query: { step: 2 } });
};

const toggleSearchBox = () => {
  showSearchBox.value = !showSearchBox.value;
  if (showSearchBox.value) {
    // 当搜索框显示时，聚焦输入框
    setTimeout(() => {
      document.querySelector('.search-input input')?.focus();
    }, 100);

    // 添加点击外部关闭事件
    setTimeout(() => {
      document.addEventListener('click', handleOutsideClick);
    }, 0);
  } else {
    // 移除点击外部关闭事件
    document.removeEventListener('click', handleOutsideClick);
  }
};

// 点击外部区域关闭搜索框
const handleOutsideClick = (event) => {
  const searchBox = document.querySelector('.search-box');
  const searchIcon = document.querySelector(
    '.header_view_action_item img[src*="search.png"]'
  );

  if (
    searchBox &&
    !searchBox.contains(event.target) &&
    searchIcon &&
    !searchIcon.contains(event.target)
  ) {
    showSearchBox.value = false;
    document.removeEventListener('click', handleOutsideClick);
  }
};

const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning(t('qing_shu_ru_sou_suo_guan_jian_ci'));
    return;
  }
  // 跳转到搜索结果页面，或在当前页面显示搜索结果
  router.push({
    path: '/goods',
    query: { keyword: searchKeyword.value },
  });
  showSearchBox.value = false; // 搜索后隐藏搜索框
};

const onChange = async (currency) => {
  try {
    // 调用接口设置币种
    // await SetCurrency(currency);

    // 更新本地UI和存储
    unit.value = currency;
    setCurrency(currency);

    // 切换币种之后发送通知，更新商品列表（首页、商品页和收藏页）
    triggerCurrencyUpdate();
  } catch (error) {
    console.error('设置币种失败:', error);
  }
};

// 判断当前路由是否匹配
const isCurrentRoute = (path) => {
  return router.currentRoute.value.path === path;
};

// 获取当前语言名称
const getCurrentLanguageName = () => {
  let lang = locale.value;
  if (lang == 'zh_cn') {
    lang = 'zh';
  } else if (lang == 'zh_hk') {
    lang = 'hk';
  }
  const currentLang = langList.value.find((item) => item.lang === lang);
  return currentLang ? currentLang.name : ''; // 如果没找到匹配项，默认显示英文
};
</script>
<style>
.el-drawer__header {
  padding: 0;
  box-sizing: border-box;
}

.el-drawer__body {
  padding: 0 2.4rem;
}

/* 强制移动设备上的抽屉宽度为100% */
@media screen and (max-width: 768px) {
  .el-drawer.rtl {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* 确保抽屉内容区域也是100%宽度 */
  .el-drawer .el-drawer__body {
    width: 100% !important;
    box-sizing: border-box;
    padding: 0 1.5rem;
  }

  /* 确保内部所有元素不会导致水平滚动 */
  .cart_view {
    width: 100%;
    box-sizing: border-box;
  }

  /* 移动端购物车字体大小调整 */
  .cart_view_item_name {
    font-size: 1.4rem !important;
    height: auto !important;
    line-height: 1.4 !important;
  }

  .cart_view_item_price {
    font-size: 1.6rem !important;
    height: auto !important;
    line-height: 1.4 !important;
  }

  .cart_view_item {
    padding: 2rem 0 !important;
  }

  .cart_view_item_img {
    width: 8rem !important;
    height: 8rem !important;
  }

  .cart_view_item_main {
    padding-left: 1.2rem !important;
  }

  .cart_view_footer_total {
    font-size: 1.4rem !important;
  }

  .cart_view_footer_submit,
  .cart_view_footer_cart {
    font-size: 1.5rem !important;
  }

  .cart_view_footer {
    padding: 0 !important;
  }

  .drawer_header {
    width: 100%;
    padding: 0 1.5rem;
  }

  .drawer_header_main {
    font-size: 1.6rem !important;
  }

  /* 调整Element Plus组件在移动端的样式 */
  .el-checkbox {
    --el-checkbox-font-size: 1.4rem !important;
  }

  .el-checkbox__label {
    font-size: 1.4rem !important;
  }

  .cart_view_item .el-input-number {
    --el-input-number-width: 10rem !important;
    --el-input-height: 3rem !important;
    --el-font-size-base: 1.4rem !important;
  }

  .cart_view_item .el-input-number__decrease,
  .cart_view_item .el-input-number__increase {
    width: 2.5rem !important;
  }

  /* 全局移动端适配样式 */
  .el-drawer__body {
    padding: 0 1.5rem !important;
    overflow-x: hidden !important;
  }

  .el-drawer__header {
    margin-bottom: 0 !important;
  }

  /* 移动端全局样式调整 */
  .cart_view_footer {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 1rem;
  }

  .cart_view_footer_checkbox {
    align-self: flex-start;
    margin-bottom: 1rem;
  }

  .cart_view_footer_total {
    align-self: flex-start;
    margin-bottom: 1rem;
  }

  /* 空购物车状态适配 */
  .isEmpty_text {
    font-size: 1.8rem !important;
  }

  .isEmpty_subtext {
    font-size: 1.4rem !important;
  }

  .isEmpty_action {
    font-size: 1.5rem !important;
    height: 4rem !important;
    width: 20rem !important;
  }

  .isEmpty_img {
    width: 15rem !important;
    height: 15rem !important;
  }
}

.loading-state {
  padding: 2rem;
  height: 60rem;
}

.cart_view_footer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart_view_footer_total {
  color: #e74c3c;
  font-weight: 600;
  font-size: 1.6rem;
}

.cart_view_item_del:hover {
  opacity: 0.7;
  cursor: pointer;
}

.cart_view_footer_submit.disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.cart-icon-container {
  position: relative;
  display: inline-block;
}

.cart-count {
  position: absolute;
  top: -0.8rem;
  right: -0.8rem;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  min-width: 1.6rem;
  height: 1.6rem;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.4rem;
}

.active-nav-item {
  color: #6e4aeb !important;
}

.mobile-settings-header {
  /* position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 100;
  width: auto; */
  background-color: #f6f6f6;
  width: 100%;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.mobile-settings-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1.5rem;
}

.mobile-dropdown {
  margin-left: 0.5rem;
}

.mobile-dropdown-trigger {
  display: flex;
  align-items: center;
  padding: 0.4rem 0.8rem;
  font-size: 1.4rem;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 0.4rem;
}

.search-box-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  padding: 0;
}

.search-box {
  position: relative;
  width: 90%;
  max-width: 44rem;
  background-color: white;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-top: 0.5rem;
}

.search-input-wrapper {
  flex: 1;
  padding: 0.8rem;
}

.search-input {
  font-size: 1.6rem;
}

.search-input .el-input__wrapper {
  background-color: #f3f4f8;
  border: none;
}

.close-search-btn {
  width: 2.4rem;
  height: 2.4rem;
  margin-right: 1.5rem;
  margin-left: 0.8rem;
  cursor: pointer;
  transition: opacity 0.2s;
}

.close-search-btn:hover {
  opacity: 1;
}

.search-icon {
  width: 2.4rem;
  height: 2.4rem;
  margin-right: 0.5rem;
}

.search-input :deep(.el-input__wrapper) {
  padding: 1.2rem;
  box-shadow: none !important;
}

.search-input :deep(.el-input__inner) {
  height: 4rem;
  font-size: 1.6rem;
}

.close-icon {
  cursor: pointer;
  font-size: 1.6rem;
  color: #999;
}

.close-icon:hover {
  color: #333;
}

/* 调整头部的定位，确保搜索框可以正确定位 */
.header {
  position: relative;
}

.header_view {
  position: relative;
}
</style>
<style lang="scss" scoped>
@import url(./app.scss);
</style>
