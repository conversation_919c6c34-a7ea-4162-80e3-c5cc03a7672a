.login_view {
  padding: 2.9rem 30.1rem 13.6rem 30.1rem;
  background: #f6f6f6;
  box-sizing: border-box;
  flex: 1;
  &_title {
    height: 1.9rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.4rem;
    color: #70707b;
    margin-bottom: 2.9rem;
  }
  &_main {
    height: 71.8rem;
    background: #ffffff;
    border-radius: 1.9rem;
    padding: 19.1rem 23.3rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    &_form {
      width: 34.9rem;
      &_title {
        height: 3.3rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 2.3rem;
        color: #242426;
        text-align: center;
        line-height: 3.3rem;
        margin-bottom: 5.8rem;
      }
      &_box {
        margin-bottom: 6.4rem;
        &_input {
          margin-bottom: 2.9rem;
          width: 100%;
          height: 5rem;
          background: #f3f4f8;
          border-radius: 3.1rem;
          position: relative;
          box-sizing: border-box;
          &_val {
            width: 100%;
            height: 5rem;
            background: #f3f4f8;
            border-radius: 3.1rem;
            padding-left: 2.3rem;
            box-sizing: border-box;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 1.6rem;
            color: #323232;
            border: 0;
          }
          &_val:focus {
            outline: 0;
          }
          &_val::placeholder {
            color: #70707b;
          }
        }
        &_input:last-child {
          margin-bottom: 0;
        }
      }
      &_tip {
        height: 2.3rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #000000;
        display: flex;
        align-items: center;
        padding-left: 2.3rem;
        display: flex;
        align-items: center;
        margin-bottom: 2.9rem;
        cursor: pointer;
        &_icon {
          width: 1.6rem;
          height: 1.6rem;
          margin-right: 0.4rem;
        }
      }
      &_btn {
        cursor: pointer;
        width: 34.9rem;
        height: 5rem;
        background: #6e4aeb;
        border-radius: 3.1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.7rem;
        color: #ffffff;
      }
    }
  }
  &_main::after {
    content: '';
    position: absolute;
    top: 19.1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 33.6rem;
    border: 0.1rem solid #e7e7e7;
  }
}
.mb12 {
  margin-bottom: 1.2rem;
}

// 移动端样式
.mobile-view {
  padding: 1.5rem;
  min-height: 100vh;
  .login_view_title {
    // text-align: center;
    margin-bottom: 1.5rem;
  }
}

.mobile-main {
  height: auto;
  min-height: 45rem;
  padding: 2rem 1.5rem;
  display: flex; // 改为块级布局，不使用flex
  background-color: #fff;
  border-radius: 1rem;

  &::after {
    display: none; // 移除分割线
  }
}

.mobile-tabs {
  display: flex;
  //   border-bottom: 1px solid #e7e7e7;
  margin-bottom: 5rem;
  width: 100%;
  padding: 0 8rem;
  box-sizing: border-box;
}

.mobile-tab {
  flex: 1;
  text-align: center;
  padding: 1rem 0;
  font-size: 1.6rem;
  font-weight: 500;
  color: #70707b;
  position: relative;
  cursor: pointer;

  &.active {
    color: #6e4aeb;
    font-weight: 600;

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 30%;
      width: 40%;
      height: 3px;
      background-color: #6e4aeb;
    }
  }
}

.mobile-container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.mobile-form {
  width: 100%;
  padding: 0;

  .login_view_main_form_box {
    margin-bottom: 2rem;
  }

  .login_view_main_form_tip {
    margin-bottom: 1.5rem;
  }
}

.mobile-btn {
  width: 100%;
  margin-top: 3.5rem;
}
